# SentryCoin V2 - Multi-Chain Whale Monitoring Configuration
# Updated for Etherscan V2 API with 50+ chain support

# ==============================================
# ETHERSCAN V2 API CONFIGURATION
# ==============================================
# Get your API key from: https://etherscan.io/apis
ETHERSCAN_API_KEY=YourApiKeyToken

# V2 API supports 50+ chains with single API key
# Supported chains: Ethereum, BSC, Polygon, Arbitrum, Optimism, Base, Avalanche, Fantom, etc.

# ==============================================
# WHALE ADDRESSES FOR MONITORING
# ==============================================
# These addresses will be monitored across ALL supported chains
# Format: 42-character hex addresses starting with 0x

# Primary whale addresses (replace with actual addresses)
WHALE_ADDRESS_1=******************************************
WHALE_ADDRESS_2=******************************************
WHALE_ADDRESS_3=******************************************
WHALE_ADDRESS_4=******************************************
WHALE_ADDRESS_5=******************************************
WHALE_ADDRESS_6=******************************************
WHALE_ADDRESS_7=******************************************
WHALE_ADDRESS_8=******************************************

# ==============================================
# MULTI-CHAIN MONITORING SETTINGS
# ==============================================

# Enable multi-chain monitoring (V2 feature)
ENABLE_MULTICHAIN_MONITORING=true

# Chains to monitor (comma-separated chain IDs)
# 1=Ethereum, 56=BSC, 137=Polygon, 42161=Arbitrum, 10=Optimism, 8453=Base
# Note: Fantom (250) and Avalanche (43114) disabled due to API instability
MONITORED_CHAINS=1,56,137,42161,10,8453

# Enable unstable chains (Avalanche, Fantom) - may cause API errors
ENABLE_UNSTABLE_CHAINS=false

# Whale activity thresholds
WHALE_HUNT_TRIGGER_THRESHOLD=3000000
WHALE_HUNT_MODE_DURATION_HOURS=12
WHALE_DUMP_VALIDITY_HOURS=6

# API rate limiting for multi-chain calls
ONCHAIN_MONITORING_INTERVAL=15000
MULTICHAIN_RATE_LIMIT_MS=100

# ==============================================
# EXCHANGE ADDRESSES (CEX DEPOSIT DETECTION)
# ==============================================
# These addresses are monitored for whale deposits (dump signals)

# Binance addresses (confirmed from transaction analysis)
BINANCE_ADDRESS_1=******************************************
BINANCE_ADDRESS_2=******************************************
BINANCE_ADDRESS_3=******************************************

# Coinbase addresses
COINBASE_ADDRESS_1=0xfe9e8709d3215310075d67e3ed32a380ccf451c8
COINBASE_ADDRESS_2=0xa9d1e08c7793af67e9d92fe308d5697fb81d3e43

# Gate.io addresses
GATEIO_ADDRESS_1=0x0d0707963952f2fba59dd06f2b425ace40b492fe
GATEIO_ADDRESS_2=0x77696bb39917c91a0c3908d577d5e322095425ca

# ==============================================
# DEBUGGING AND LOGGING
# ==============================================

# Enable detailed multi-chain logging
ENABLE_MULTICHAIN_LOGGING=true

# Log whale activity across chains
LOG_WHALE_ACTIVITY=true

# API performance monitoring
TRACK_API_PERFORMANCE=true

# ==============================================
# FALLBACK CONFIGURATION
# ==============================================

# Fallback to single-chain if V2 API fails
ENABLE_SINGLE_CHAIN_FALLBACK=true

# Primary chain for fallback (1 = Ethereum)
FALLBACK_CHAIN_ID=1

# ==============================================
# USAGE INSTRUCTIONS
# ==============================================
# 1. Get your Etherscan API key from https://etherscan.io/apis
# 2. Replace WHALE_ADDRESS_1 through WHALE_ADDRESS_8 with actual whale addresses
# 3. Set ETHERSCAN_API_KEY to your actual API key
# 4. Copy this file to your project root as .env or merge with existing .env
# 5. Restart your SentryCoin application

# Example API key format:
# ETHERSCAN_API_KEY=VZFDUWB3YGQ1YCDKTCU1D6DDSS

# Example whale address format:
# WHALE_ADDRESS_1=******************************************
